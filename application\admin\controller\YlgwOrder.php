<?php

namespace app\admin\controller;


use app\common\controller\Backend;
/**
 * 开通养老顾问记录
 *
 * @icon fa fa-user-plus
 */
class YlgwOrder extends Backend
{

    /**
     * YlgwOrder模型对象
     * @var \app\admin\model\xiluedu\CourseOrder
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\xiluedu\CourseOrder;
        
        // 定义开通方式列表
        $this->view->assign("platformList", [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            'ylgw' => '养老顾问开通',
            'wxmin' => '自主购买(小程序)',
            'h5' => '自主购买(H5)',
            'app' => '自主购买(APP)'
        ]);
        
        // 定义支付状态列表
        $this->view->assign("payStatusList", [
            '1' => '待支付',
            '2' => '已支付'
        ]);
        
        // 定义支付方式列表
        $this->view->assign("payTypeList", [
            '0' => '无',
            '1' => '微信',
            '2' => '余额',
            '3' => '额度支付'
        ]);
    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 只查询课程ID为16的订单（养老顾问课程）
            $where = is_array($where) ? $where : [];
            $where['course_id'] = 16;

            $list = $this->model
                    ->with(['user', 'course'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id', 'nickname', 'mobile', 'is_sqdl', 'is_qydl', 'is_ylgw']);
                $row->getRelation('course')->visible(['id', 'name']);
                
                // 添加开通方式描述
                $row->platform_text = $this->getPlatformText($row->platform);
                
                // 添加开通者信息（如果是额度开通）
                if ($row->platform == 'quota') {
                    $opener = $this->getOpenerInfo($row->user_id);
                    $row->opener_info = $opener;
                }
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取平台文本描述
     */
    private function getPlatformText($platform)
    {
        $platformList = [
            'quota' => '额度开通',
            'admin' => '管理员开通', 
            'ylgw' => '养老顾问开通',
            'wxmin' => '自主购买(小程序)',
            'h5' => '自主购买(H5)',
            'app' => '自主购买(APP)'
        ];
        
        return isset($platformList[$platform]) ? $platformList[$platform] : $platform;
    }

    /**
     * 获取开通者信息（用于额度开通）
     */
    private function getOpenerInfo($user_id)
    {
        $user = \app\common\model\User::where('id', $user_id)->find();
        if (!$user || !$user->parent_id) {
            return null;
        }
        
        $parent = \app\common\model\User::where('id', $user->parent_id)->find();
        if (!$parent) {
            return null;
        }
        
        $role = '';
        if ($parent->is_qydl == 1) {
            $role = '城市负责人';
        } elseif ($parent->is_sqdl == 1) {
            $role = '养老院长';
        }
        
        return [
            'id' => $parent->id,
            'nickname' => $parent->nickname,
            'mobile' => $parent->mobile,
            'role' => $role
        ];
    }

    /**
     * 禁用添加功能
     */
    public function add()
    {
        $this->error(__('Operation not allowed'));
    }

    /**
     * 禁用编辑功能
     */
    public function edit($ids = null)
    {
        $this->error(__('Operation not allowed'));
    }

    /**
     * 禁用删除功能
     */
    public function del($ids = "")
    {
        $this->error(__('Operation not allowed'));
    }
}
