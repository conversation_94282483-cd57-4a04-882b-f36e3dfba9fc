<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="platform">
            <li class="{:$Think.get.platform === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            <li class="{:$Think.get.platform === 'quota' ? 'active' : ''}"><a href="#t-quota" data-value="quota" data-toggle="tab">额度开通</a></li>
            <li class="{:$Think.get.platform === 'admin' ? 'active' : ''}"><a href="#t-admin" data-value="admin" data-toggle="tab">管理员开通</a></li>
            <li class="{:$Think.get.platform === 'ylgw' ? 'active' : ''}"><a href="#t-ylgw" data-value="ylgw" data-toggle="tab">养老顾问开通</a></li>
            <li class="{:$Think.get.platform === 'self' ? 'active' : ''}"><a href="#t-self" data-value="wxmin,h5,app" data-toggle="tab">自主购买</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <!-- 禁用添加、编辑、删除按钮 -->
                        <div class="dropdown btn-group {:$auth->check('ylgw_order/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=normal"><i class="fa fa-eye"></i> {:__('Set to normal')}</a></li>
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status=hidden"><i class="fa fa-eye-slash"></i> {:__('Set to hidden')}</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
